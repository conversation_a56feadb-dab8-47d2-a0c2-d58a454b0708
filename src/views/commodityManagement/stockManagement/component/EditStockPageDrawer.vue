<template>
    <YDrawer
        v-model:open="drawerOpen"
        title="修改库存"
        @close="cancel"
        :footerStyle="{ display: 'none' }"
    >
        <div class="drawer-content">
            <div class="table_box">
                <!-- 表格 -->
                <ETable
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                @click="addDrawerFn('edit')"
                                >编辑</a-button
                            >
                            <a-button type="link" class="btn-link-color">删除</a-button>
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
    </YDrawer>
</template>

<script setup>
import { ref, computed, nextTick } from "vue"

import { PlusOutlined } from "@ant-design/icons-vue"

const drawerOpen = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "商品条码", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "商品名称", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "价格（元）", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "商品类型", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "当前库存", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "调整库存", dataIndex: "createTime", key: "createTime", width: 100 }
])

// 打开抽屉
const open = () => {
    drawerOpen.value = true
}

// 关闭抽屉
const cancel = () => {
    drawerOpen.value = false
}

// 暴露给父组件的方法
defineExpose({
    open
})
</script>

<style lang="less" scoped>
.drawer-content {
    background-color: #ffffff;
}
</style>
