<template>
    <div class="page_content">
        <div class="content_box">
            <!-- 头部 -->
            <div class="card_head">
                <span class="title">商品列表</span>
            </div>
            <div class="content_page">
                <!-- 搜索组件区域 -->
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />

                <!-- 按钮区域 -->
                <div class="btn_group">
                    <a-button type="primary" @click="addDrawerFn('add', null)">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        新建商品
                    </a-button>
                    <a-button @click="commodityTypeDrawerRef.open()"
                        >商品类型管理</a-button
                    >
                    <a-button>导入商品</a-button>
                    <a-button>批量删除</a-button>
                    <a-button>批量上架</a-button>
                    <a-button>批量下架</a-button>
                </div>
                <div class="table_box">
                    <!-- 表格 -->
                    <ETable
                        :columns="columns"
                        :data-source="state.dataSource"
                        :paginations="state.pagination"
                        @change="handleTableChange"
                        :loading="state.tableLoading"
                        :row-selection="{
                            selectedRowKeys: state.selectedRowKeys,
                            onChange: onSelectChange
                        }"
                    >
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.dataIndex == 'operate'">
                                <a-button
                                    type="link"
                                    class="btn-link-color"
                                    @click="addDrawerFn('detail')"
                                    >详情</a-button
                                >
                                <a-button
                                    type="link"
                                    class="btn-link-color"
                                    @click="addDrawerFn('edit')"
                                    >编辑</a-button
                                >
                            </template>
                        </template>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
    <CommodityDrawer
        ref="commodityDrawerRef"
        @submitDrawer="handleSubmitDrawer"
    ></CommodityDrawer>
    <CommodityTypeDrawer ref="commodityTypeDrawerRef"></CommodityTypeDrawer>
</template>

<script setup>
import { reactive } from "vue"
import { useRouter, useRoute } from "vue-router"
import CommodityDrawer from "./component/CommodityDrawer.vue"
import CommodityTypeDrawer from "./component/CommodityTypeDrawer.vue"

const router = useRouter()
const route = useRoute()
const commodityDrawerRef = ref(null)
const commodityTypeDrawerRef = ref(null)
const query = ref({})

const state = reactive({
    tableLoading: false,
    selectedRowKeys: [],
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "商品条码", dataIndex: "majorName", key: "majorName" },
    {
        title: "商品名称",
        dataIndex: "educationalSystem",
        key: "educationalSystem"
    },
    { title: "价格（元）", dataIndex: "createBy", key: "createBy" },
    {
        title: "剩余库存",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "商品类型",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "商品状态",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "更新时间",
        dataIndex: "createTime",
        key: "createTime"
    },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

// 模拟商品数据
const mockCommodityData = {
    id: 1,
    name: "香草拿铁",
    barcode: "1234567890123",
    categoryId: 1,
    price: 25.5,
    stock: 100,
    status: 1,
    description: "香浓的香草拿铁，选用优质咖啡豆，口感丝滑，香味浓郁。",
    imageUrl:
        "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
}

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "商品条码"
    },
    {
        type: "input",
        value: "majorName",
        label: "商品名称"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "商品类型",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "select",
        value: "machineStatus",
        label: "商品状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    }
])

const editCardRef = ref(null)
const cardInfoRef = ref(null)
const giveCardRef = ref(null)

const getList = () => {
    // http.post("/unicard/merchant-product/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    state.dataSource = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getList()
}

const getInitList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

// 打开抽屉
const addDrawerFn = (type, record = {}) => {
    if (type === "add") {
        // 新建商品
        commodityDrawerRef.value?.open("add")
    } else if (type === "edit") {
        // 编辑商品
        commodityDrawerRef.value?.open("edit", mockCommodityData)
    } else if (type === "detail") {
        // 商品详情
        commodityDrawerRef.value?.open("detail", mockCommodityData)
    }
}

// 处理商品抽屉提交事件
const handleSubmitDrawer = () => {
    // 刷新商品列表
    getInitList()
}
onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .btn_group {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 16px;
            }
            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
